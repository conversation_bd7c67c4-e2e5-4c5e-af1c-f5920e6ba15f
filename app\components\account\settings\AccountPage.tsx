

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import ProfileForm from "./ProfileForm";
import PasswordForm from "./PasswordForm";
import PreferencesForm from "./PreferencesForm";
import SecuritySettings from "./SecuritySettings";
import { AccountSettingsData, UserSecurityInfo, UserAuditLogEntry, UserDevice } from "@/app/getData/account-settings";

interface AccountPageProps {
  accountData: AccountSettingsData;
  securityInfo: UserSecurityInfo | null;
  auditLogs: UserAuditLogEntry[];
  ssoProvider: string | null;
  devices: UserDevice[];
}

export default function AccountPage({ accountData, securityInfo, auditLogs, ssoProvider, devices }: AccountPageProps) {

  return (
    <div className="space-y-6">
      <header className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Setari Cont</h1>
          <p className="text-muted-foreground">
            Gestionati informatiile contului si preferintele de securitate
          </p>
        </div>
      </header>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="profile">Profil</TabsTrigger>
          <TabsTrigger value="password">Parola</TabsTrigger>
          <TabsTrigger value="preferences">Preferinte</TabsTrigger>
          <TabsTrigger value="security">Securitate</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <ProfileForm accountData={accountData} />
        </TabsContent>

        <TabsContent value="password" className="space-y-6">
          <PasswordForm
            ssoProvider={ssoProvider}
          />
        </TabsContent>

        <TabsContent value="preferences" className="space-y-6">
          <PreferencesForm accountData={accountData} />
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <SecuritySettings
            securityInfo={securityInfo}
            ssoProvider={ssoProvider}
            devices={devices}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}