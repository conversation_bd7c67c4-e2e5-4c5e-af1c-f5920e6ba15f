"server-only"

import { ReadonlyHeaders } from "next/dist/server/web/spec-extension/adapters/headers";
import { prisma, withRetry } from "./db";
import { logger } from "./logger";
import { WebhookEvent } from "@clerk/nextjs/server";
import { UserJSON, DeletedObjectJSON } from "@clerk/nextjs/server";
import { findOrCreateUser } from "./sync-user";
import { isValidEmail, isValidImageUrl, isValidName, maskEmail } from "./utils";


interface PrivateMetaData {
  emailNotifications?: boolean;
  pushNotifications?: boolean;
  smsNotifications?: boolean;
  newsletterOptIn?: boolean;
  inactiveReason?: string,
  inactiveBy?: string,
  inactiveAt?: Date,
}

interface ExistingUser {
  id: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  profileImage: string | null;
  phoneNumber: string | null;
  passwordEnabled: boolean | null;
  twoFactorEnabled: boolean | null;
  mfaEnabledAt: Date | null;
  mfaDisabledAt: Date | null;
  totpEnabled: boolean | null;
  emailVerified: Date | null;
  phoneVerified: boolean | null;
  isSuspended: boolean;
  isActive: boolean;
  legal_accepted_at: Date | null;
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  newsletterOptIn: boolean;
  lockoutUntil: Date | null; 
  inactiveAt: Date | null;
  inactiveReason: string | null;
  inactiveBy: string | null;
}

interface UserUpdateData {
  email?: string;
  firstName?: string;
  lastName?: string;
  profileImage?: string;
  passwordEnabled?: boolean;
  twoFactorEnabled?: boolean;
  mfaEnabledAt?: Date | null;
  mfaDisabledAt?: Date | null;
  totpEnabled?: boolean;
  emailVerified?: Date | null;
  phoneVerified?: boolean;
  isActive?: boolean;
  isSuspended?: boolean;
  phoneNumber?: string | null;
  legal_accepted_at?: Date | null;
  emailNotifications?: boolean;
  pushNotifications?: boolean;
  smsNotifications?: boolean;
  newsletterOptIn?: boolean;
  lockoutUntil?: Date | null; 
  inactiveAt?: Date | null;
  inactiveReason?: string | null;
  inactiveBy?: string | null;
}

interface AuditLogEntry {
  action: string;
  details: string;
}

interface DetectedChanges {
  hasChanges: boolean;
  updateData: UserUpdateData;
  auditLogs: AuditLogEntry[];
  changeTypes: string[];
}

interface ClerkPhoneNumber {
  id?: string;
  phone_number?: string | null;
  verification?: {
    status: 'unverified' | 'verified' | 'failed' | 'expired';
    strategy?: 'phone_code' | 'email_code' | 'reset_password_email_code';
    channel?: 'sms';
    attempts?: number;
    expire_at?: number;
  };
  reserved?: boolean;
  reserved_for_second_factor?: boolean;
  default_second_factor?: boolean;
  created_at?: number;
  updated_at?: number;
}

export interface AuditLogData {
  userId: string;
  action: string;
  entityType: string;
  entityId: string;
  details: string;
  ipAddress: string | null;
  userAgent: string | null;
  createdAt: Date;
  performedBy: string;
}

// Define specific webhook event types using type intersections
type UserUpdatedEvent = WebhookEvent & {
  type: 'user.updated' | 'user.created';
  data: ExtendedUserJSON;
}

//extend UserJSON with the new fields : mfa_enabled_at, mfa_disabled_at TEST
interface ExtendedUserJSON extends UserJSON {
  mfa_enabled_at: Date | null;
  mfa_disabled_at: Date | null;
}

type UserDeletedEvent = WebhookEvent & {
  type: 'user.deleted';
  data: DeletedObjectJSON;
};

export async function handleUserCreated(
  evt: WebhookEvent, 
  headerPayload: ReadonlyHeaders
): Promise<void> {
  // Type assertion to ensure we have the right event type
  const createEvent = evt as UserUpdatedEvent;
  const { id, email_addresses, first_name, last_name, image_url, phone_numbers, two_factor_enabled, totp_enabled, password_enabled, mfa_enabled_at, mfa_disabled_at, legal_accepted_at } = createEvent.data;
  console.log("mfa_enabled_at", mfa_enabled_at);
  try {
    const dbUser = await findOrCreateUser({
      externalId: id as string,
      email: email_addresses?.[0]?.email_address || '',
      firstName: first_name || '',
      lastName: last_name || '',
      profileImage: image_url || '',
      updateLoginStats: false, // Don't update login stats for webhook events
      twoFactorEnabled: two_factor_enabled || false,
      phoneNumber: phone_numbers?.[0]?.phone_number || '',
      mfaEnabledAt: mfa_enabled_at || null,
      mfaDisabledAt: mfa_disabled_at || null,
      passwordEnabled: password_enabled || null,
      totpEnabled: totp_enabled || false,
      emailVerified: email_addresses?.[0]?.verification?.status === 'verified' ? new Date() : null,
    });
    
    if (!dbUser) {
      logger.warn(`[Clerk Webhook] User not created for user.created: ${id}`);
      return;
    }
    
    // Create audit log for user creation
    await withRetry(() => prisma.userAuditLog.create({
      data: {
        userId: dbUser.id,
        action: 'user.created',
        entityType: 'user',
        entityId: id as string,
        details: JSON.stringify({
          message: 'User account created',
          email: email_addresses?.[0]?.email_address,
          firstName: first_name,
          lastName: last_name,
          hasProfileImage: !!image_url,
          timestamp: new Date().toISOString()
        }),
        ipAddress: headerPayload.get('x-forwarded-for') || null,
        userAgent: headerPayload.get('user-agent') || null,
        performedBy: 'clerkWebhook',
      }
    }));
    
    logger.info(`[Clerk Webhook] user.created processed for ID: ${id}`);
  } catch (error) {
    logger.error(`[Clerk Webhook] Error processing user.created for ID: ${id}:`, error);
    throw error;
  }
}

export async function handleUserUpdated(
  evt: WebhookEvent, 
  headerPayload: ReadonlyHeaders
): Promise<void> {

  // Type assertion to ensure we have the right event type
  const userEvent = evt as UserUpdatedEvent;
  
  const { id } = userEvent.data;

  // Get current user data for comparison
  const existingUser = await withRetry(() => 
    prisma.user.findUnique({
      where: { externalId: id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        profileImage: true,
        phoneNumber: true,
        twoFactorEnabled: true,
        mfaEnabledAt: true,
        mfaDisabledAt: true,
        passwordEnabled: true,
        totpEnabled: true,
        emailVerified: true,
        isSuspended: true,
        isActive: true,
        legal_accepted_at: true,
        emailNotifications: true,
        pushNotifications: true,
        smsNotifications: true,
        newsletterOptIn: true,
        lockoutUntil: true,
        inactiveAt: true,
        inactiveReason: true,
        inactiveBy: true,
      }
    })
  );

  if (!existingUser) {
    logger.warn(`[Clerk Webhook] User not found for update: ${id}`);
    return;
  }

  // Detect and validate changes
  const changes = await detectAndValidateChanges(userEvent.data, existingUser);
  
  if (changes.hasChanges) {
    // Update only changed fields
    const updatedUser = await withRetry(() => prisma.user.update({
      where: { externalId: id },
      data: {
        ...changes.updateData,
      },
    }));

    // Create specific audit logs for each change
    await createAuditLogsForChanges(changes.auditLogs, updatedUser.id, headerPayload);
    
    logger.info(`[Clerk Webhook] user.updated processed for ID: ${id}, changes: ${changes.changeTypes.join(', ')}`);
  } else {
    logger.debug(`[Clerk Webhook] user.updated received for ID: ${id} but no relevant changes detected`);
  }
}

export async function handleUserDeleted(
  evt: WebhookEvent, 
  headerPayload: ReadonlyHeaders
): Promise<void> {
  // Type assertion to ensure we have the right event type
  const deleteEvent = evt as UserDeletedEvent;
  const { id } = deleteEvent.data;

  if(!id){
    logger.warn(`[Clerk Webhook] No clerkId provided to delete`);
    return;
  }

  try {

    //get the userId from our database
    const userId = await withRetry(() => prisma.user.findUnique({
      where: { externalId: id },
      select: { id: true, email: true }
    }));

    if (!userId) {
      logger.warn(`[Clerk Webhook] User not found in database to delete the clerkId: ${id}`);
      return;
    }

    // Soft delete making the account inactive
    await withRetry(() => prisma.user.update({
      where: { id: userId.id },
      data: {
        //we dont delete the account, we make it inactive for 30 days, the trigger  will delete it or the admin will activate it
        isActive: false,
        inactiveBy: userId.email,
        inactiveAt: new Date(),
        inactiveReason: 'Deleted from Clerk',
      },
    }));

    // Create audit log
    await withRetry(() => prisma.userAuditLog.create({
      data: {
        userId: id,
        action: 'user.deleted',
        entityType: 'user',
        entityId: id,
        details: JSON.stringify({
          message: `User account deleted from clerk but not from our database(is inactive). clerkId: ${id} and userId: ${userId.id} and email ${userId.email}`,
          timestamp: new Date().toISOString()
        }),
        ipAddress: headerPayload.get('x-forwarded-for') || null,
        userAgent: headerPayload.get('user-agent') || null,
        performedBy: 'clerkWebhook', // Set the performedBy field to 'clerkWebhook' for audit purposes
      }
    }));

    logger.info(`[Clerk Webhook] user.deleted processed for ID: ${id}`);
  } catch (error) {
    logger.error(`[Clerk Webhook] Error processing user.deleted for ID: ${id}:`, error);
    throw error;
  }
}

async function detectAndValidateChanges(
  eventData: ExtendedUserJSON, 
  existingUser: ExistingUser
): Promise<DetectedChanges> {
  
  const changes: DetectedChanges = {
    hasChanges: false,
    updateData: {},
    auditLogs: [],
    changeTypes: []
  };

  const timestamp = new Date();

   if (eventData.phone_numbers !== undefined) {
    const phoneNumbers = eventData.phone_numbers as ClerkPhoneNumber[];
    
    // Find the primary or first verified phone number
    const primaryPhone = phoneNumbers.find(phone => 
      phone.verification?.status === 'verified'
    ) || phoneNumbers[0]; // Fallback to first phone if none verified
    
    const newPhoneNumber = primaryPhone?.phone_number || null;
    const currentPhoneNumber = existingUser.phoneNumber;
    
    // Check if phone number has changed
    if (newPhoneNumber !== currentPhoneNumber) {
      changes.updateData.phoneNumber = newPhoneNumber;
      changes.hasChanges = true;
      changes.changeTypes.push('phoneNumber');
      
      // Determine the type of change
      if (!currentPhoneNumber && newPhoneNumber) {
        // Phone number added
        changes.auditLogs.push({
          action: 'phone.added',
          details: JSON.stringify({
            message: `Phone number added: ${maskPhoneNumber(newPhoneNumber)}`,
            phoneNumber: maskPhoneNumber(newPhoneNumber),
            verified: primaryPhone?.verification?.status === 'verified',
            timestamp: timestamp.toISOString()
          })
        });
      } else if (currentPhoneNumber && !newPhoneNumber) {
        // Phone number removed
        changes.auditLogs.push({
          action: 'phone.removed',
          details: JSON.stringify({
            message: `Phone number removed`,
            previousPhoneNumber: maskPhoneNumber(currentPhoneNumber),
            timestamp: timestamp.toISOString()
          })
        });
      } else if (currentPhoneNumber && newPhoneNumber) {
        // Phone number changed
        changes.auditLogs.push({
          action: 'phone.changed',
          details: JSON.stringify({
            message: `Phone number changed`,
            previousPhoneNumber: maskPhoneNumber(currentPhoneNumber),
            newPhoneNumber: maskPhoneNumber(newPhoneNumber),
            verified: primaryPhone?.verification?.status === 'verified',
            timestamp: timestamp.toISOString()
          })
        });
      }
    }
    
    // Check for verification status change on existing phone
    if (newPhoneNumber === currentPhoneNumber && newPhoneNumber) {
      const isVerified = primaryPhone?.verification?.status === 'verified';
      const wasVerified = existingUser.phoneVerified || false; // Assuming you have this field
      
      if (isVerified && !wasVerified) {
        changes.auditLogs.push({
          action: 'phone.verified',
          details: JSON.stringify({
            message: `Phone number verified: ${maskPhoneNumber(newPhoneNumber)}`,
            phoneNumber: maskPhoneNumber(newPhoneNumber),
            verificationMethod: primaryPhone?.verification?.strategy || 'phone_code',
            timestamp: timestamp.toISOString()
          })
        });
        
        // If you track phone verification status separately
        if ('phoneVerified' in existingUser) {
          changes.updateData.phoneVerified = true;
          changes.hasChanges = true;
          changes.changeTypes.push('phoneVerified');
        }
      }
    }
  }

  // Notification preferences change detection
  if (eventData.private_metadata && hasNotificationPreferences(eventData.private_metadata)) {
    const privateMetadata = eventData.private_metadata as PrivateMetaData;
    
    //InactiveAt
    if ('inactiveAt' in privateMetadata && privateMetadata.inactiveAt !== undefined) {
      const newInactiveAt = privateMetadata.inactiveAt;
      
      if (newInactiveAt !== existingUser.inactiveAt) {
        changes.updateData.inactiveAt = newInactiveAt;
        changes.hasChanges = true;
        changes.changeTypes.push('inactiveAt');
        changes.auditLogs.push({
          action: 'account.inactivated',
          details: JSON.stringify({
            message: `Account inactivated`,
            previousValue: existingUser.inactiveAt,
            newValue: newInactiveAt,
            timestamp: timestamp.toISOString(),
            method: 'clerk_user_profile'
          })
        });
      }
    }

    //InactiveReason
    if ('inactiveReason' in privateMetadata && privateMetadata.inactiveReason !== undefined) {
      const newInactiveReason = privateMetadata.inactiveReason;
      
      if (newInactiveReason !== existingUser.inactiveReason) {
        changes.updateData.inactiveReason = newInactiveReason;
        changes.hasChanges = true;
        changes.changeTypes.push('inactiveReason');
        changes.auditLogs.push({
          action: 'account.inactivated',
          details: JSON.stringify({
            message: `Account inactivated reason changed`,
            previousValue: existingUser.inactiveReason,
            newValue: newInactiveReason,
            timestamp: timestamp.toISOString(),
            method: 'clerk_user_profile'
          })
        });
      }
    }

    //InactiveBy
    if ('inactiveBy' in privateMetadata && privateMetadata.inactiveBy !== undefined) {
      const newInactiveBy = privateMetadata.inactiveBy;
      
      if (newInactiveBy !== existingUser.inactiveBy) {
        changes.updateData.inactiveBy = newInactiveBy;
        changes.hasChanges = true;
        changes.changeTypes.push('inactiveBy');
        changes.auditLogs.push({
          action: 'account.inactivated',
          details: JSON.stringify({
            message: `Account inactivated by ${newInactiveBy}`,
            previousValue: existingUser.inactiveBy,
            newValue: newInactiveBy,
            timestamp: timestamp.toISOString(),
            method: 'clerk_user_profile'
          })
        });
      }
    }

        // Email notifications
    if ('emailNotifications' in privateMetadata && privateMetadata.emailNotifications !== undefined) {
      const newEmailNotifications = extractBooleanPreference(privateMetadata.emailNotifications);
      
      if (newEmailNotifications !== existingUser.emailNotifications) {
        changes.updateData.emailNotifications = newEmailNotifications;
        changes.hasChanges = true;
        changes.changeTypes.push('emailNotifications');
        changes.auditLogs.push({
          action: 'preferences.update.email',
          details: JSON.stringify({
            message: `Email notifications ${newEmailNotifications ? 'enabled' : 'disabled'}`,
            previousValue: existingUser.emailNotifications,
            newValue: newEmailNotifications,
            timestamp: timestamp.toISOString(),
            method: 'clerk_user_profile'
          })
        });
      }
    }


    // Push notifications
    if ('pushNotifications' in privateMetadata && privateMetadata.pushNotifications !== undefined) {
      const newPushNotifications = extractBooleanPreference(privateMetadata.pushNotifications);
      
      if (newPushNotifications !== existingUser.pushNotifications) {
        changes.updateData.pushNotifications = newPushNotifications;
        changes.hasChanges = true;
        changes.changeTypes.push('pushNotifications');
        changes.auditLogs.push({
          action: 'preferences.update.push',
          details: JSON.stringify({
            message: `Push notifications ${newPushNotifications ? 'enabled' : 'disabled'}`,
            previousValue: existingUser.pushNotifications,
            newValue: newPushNotifications,
            timestamp: timestamp.toISOString(),
            method: 'clerk_user_profile'
          })
        });
      }
    }

    // SMS notifications
    if ('smsNotifications' in privateMetadata && privateMetadata.smsNotifications !== undefined) {
      const newSmsNotifications = extractBooleanPreference(privateMetadata.smsNotifications);
      
      if (newSmsNotifications !== existingUser.smsNotifications) {
        changes.updateData.smsNotifications = newSmsNotifications;
        changes.hasChanges = true;
        changes.changeTypes.push('smsNotifications');
        changes.auditLogs.push({
          action: 'preferences.update.sms',
          details: JSON.stringify({
            message: `SMS notifications ${newSmsNotifications ? 'enabled' : 'disabled'}`,
            previousValue: existingUser.smsNotifications,
            newValue: newSmsNotifications,
            timestamp: timestamp.toISOString(),
            method: 'clerk_user_profile'
          })
        });
      }
    }

    // Newsletter opt-in
    if ('newsletterOptIn' in privateMetadata && privateMetadata.newsletterOptIn !== undefined) {
      const newNewsletterOptIn = extractBooleanPreference(privateMetadata.newsletterOptIn);
      
      if (newNewsletterOptIn !== existingUser.newsletterOptIn) {
        changes.updateData.newsletterOptIn = newNewsletterOptIn;
        changes.hasChanges = true;
        changes.changeTypes.push('newsletterOptIn');
        changes.auditLogs.push({
          action: 'preferences.update.newsletter',
          details: JSON.stringify({
            message: `Newsletter ${newNewsletterOptIn ? 'subscribed' : 'unsubscribed'}`,
            previousValue: existingUser.newsletterOptIn,
            newValue: newNewsletterOptIn,
            timestamp: timestamp.toISOString(),
            method: 'clerk_user_profile'
          })
        });
      }
    }
  }

    //banned from clerk change detection with isActive from DB
  if (eventData.banned !== undefined) {
    const shouldBeActive = !eventData.banned; // Convert banned to isActive logic
    
    if (shouldBeActive !== existingUser.isActive) { // Only if there's an actual change
      changes.updateData.isActive = shouldBeActive;
      changes.hasChanges = true;
      changes.changeTypes.push('banned');
      changes.auditLogs.push({
        action: eventData.banned ? 'account.banned' : 'account.unbanned',
        details: JSON.stringify({
          message: eventData.banned ? 'Account banned' : 'Account unbanned',
          timestamp: timestamp.toISOString()
        })
      });
    }
  }

  //locked from clerk change detection with isSuspended from DB
  if (eventData.locked !== undefined || eventData.lockout_expires_in_seconds !== undefined) {
    const isCurrentlyLocked = eventData.locked === true;
    const lockoutExpiresInSeconds = eventData.lockout_expires_in_seconds;
    
    // Calculate lockout expiration time
    let newLockoutUntil: Date | null = null;
    if (isCurrentlyLocked && lockoutExpiresInSeconds && lockoutExpiresInSeconds > 0) {
      // Account is locked with a specific duration
      newLockoutUntil = new Date(Date.now() + (lockoutExpiresInSeconds * 1000));
    }
    
    // Check if there's a change in suspension status or lockout time
    const suspensionChanged = isCurrentlyLocked !== existingUser.isSuspended;
    const lockoutTimeChanged = (newLockoutUntil?.getTime() || null) !== (existingUser.lockoutUntil?.getTime() || null);
    
    if (suspensionChanged || lockoutTimeChanged) {
      changes.updateData.isSuspended = isCurrentlyLocked;
      changes.updateData.lockoutUntil = newLockoutUntil;
      changes.hasChanges = true;
      changes.changeTypes.push('locked');
      
      // Determine the specific action
      let action = '';
      let message = '';
      
      if (!existingUser.isSuspended && isCurrentlyLocked) {
        // Account just got locked
        action = 'account.locked';
        message = newLockoutUntil 
          ? `Account locked until ${newLockoutUntil.toISOString()}`
          : 'Account locked indefinitely';
      } else if (existingUser.isSuspended && !isCurrentlyLocked) {
        // Account unlocked (either expired or manually cleared)
        action = 'account.unlocked';
        const wasManuallyCleared = existingUser.lockoutUntil && existingUser.lockoutUntil > timestamp;
        message = wasManuallyCleared 
          ? 'Account lockout manually cleared by admin'
          : 'Account lockout expired';
      } else if (isCurrentlyLocked && lockoutTimeChanged) {
        // Lockout duration changed while still locked
        action = 'account.lockout_extended';
        message = `Account lockout extended until ${newLockoutUntil?.toISOString() || 'indefinitely'}`;
      }
      
      changes.auditLogs.push({
        action,
        details: JSON.stringify({
          message,
          locked: isCurrentlyLocked,
          lockoutUntil: newLockoutUntil?.toISOString() || null,
          lockoutExpiresInSeconds: lockoutExpiresInSeconds || null,
          previousLockoutUntil: existingUser.lockoutUntil?.toISOString() || null,
          timestamp: timestamp.toISOString()
        })
      });
    }
  }

  const primaryEmailObject = eventData.email_addresses?.find(
    (e) => e.id === eventData.primary_email_address_id
  );

  if (primaryEmailObject) {
    const newEmail = primaryEmailObject.email_address;
    const isVerified = primaryEmailObject.verification?.status === 'verified';

    // Check if the email address in your DB is different from the new primary email from Clerk.
    if (newEmail && newEmail !== existingUser.email) {
      changes.updateData.email = newEmail;
      changes.hasChanges = true;
      changes.changeTypes.push('email');
      changes.auditLogs.push({
        action: 'email.change',
        details: JSON.stringify({
          message: `Email address changed from ${existingUser.email} to ${newEmail}`,
          previousEmail: maskEmail(existingUser.email),
          newEmail: maskEmail(newEmail),
        }),
      });

      // Since the email has changed, we must also update its verification status.
      // This ensures the verification status always corresponds to the *current* email.
      const newVerificationStatus = isVerified ? new Date() : null;
      if (newVerificationStatus !== existingUser.emailVerified) {
        changes.updateData.emailVerified = newVerificationStatus;
        // No need to push a separate audit log here as it's part of the email change event.
      }
    } else {
      // This block handles the case where the email address is the same,
      // but its verification status might have changed.
      const newVerificationStatus = isVerified ? new Date() : null;
      const wasVerified = !!existingUser.emailVerified;

      if (newVerificationStatus !== null && !wasVerified) {
        changes.updateData.emailVerified = newVerificationStatus;
        changes.hasChanges = true;
        changes.changeTypes.push('email.verified');
        changes.auditLogs.push({
          action: 'email.verified',
          details: JSON.stringify({
            message: `Email ${newEmail} was verified.`,
          }),
        });
      }
    }
  }

  
  // First name change detection
  const newFirstName = eventData.first_name;
  if (newFirstName !== undefined && 
      newFirstName !== existingUser.firstName && 
      isValidName(newFirstName)) {
    changes.updateData.firstName = newFirstName || '';
    changes.hasChanges = true;
    changes.changeTypes.push('firstName');
    changes.auditLogs.push({
      action: 'profile.firstName.change',
      details: JSON.stringify({
        message: `First name changed from ${existingUser.firstName} to ${newFirstName}`,
        timestamp: timestamp.toISOString()
      })
    });
  }

  // Last name change detection
  const newLastName = eventData.last_name;
  if (newLastName !== undefined && 
      newLastName !== existingUser.lastName && 
      isValidName(newLastName)) {
    changes.updateData.lastName = newLastName || '';
    changes.hasChanges = true;
    changes.changeTypes.push('lastName');
    changes.auditLogs.push({
      action: 'profile.lastName.change',
      details: JSON.stringify({
        message: `Last name changed from ${existingUser.lastName} to ${newLastName}`,
        timestamp: timestamp.toISOString()
      })
    });
  }

  // Profile image change detection
  if (eventData.image_url !== undefined && 
      eventData.image_url !== existingUser.profileImage && 
      isValidImageUrl(eventData.image_url)) {
    changes.updateData.profileImage = eventData.image_url || '';
    changes.hasChanges = true;
    changes.changeTypes.push('profileImage');
    changes.auditLogs.push({
      action: 'profile.image.change',
      details: JSON.stringify({
        message: `Profile image changed from ${existingUser.profileImage} to ${eventData.image_url}`,
        hasImage: !!eventData.image_url,
        timestamp: timestamp.toISOString()
      })
    });
  }

  // Password change detection
  if (eventData.password_enabled !== undefined && 
      eventData.password_enabled !== existingUser.passwordEnabled) {
    changes.updateData.passwordEnabled = eventData.password_enabled;
    changes.hasChanges = true;
    changes.changeTypes.push('password');
    changes.auditLogs.push({
      action: 'password.change',
      details: JSON.stringify({
        message: `Password changed`,
        timestamp: timestamp.toISOString(),
        method: 'clerk_user_profile'
      })
    });
  }

  // TOTP change detection
  if (eventData.totp_enabled !== undefined && 
    eventData.totp_enabled !== existingUser.totpEnabled) {
      changes.updateData.totpEnabled = eventData.totp_enabled;
      changes.hasChanges = true;
      changes.changeTypes.push('totp');
      changes.auditLogs.push({
        action: 'totp.change',
        details: JSON.stringify({
          message: `TOTP changed`,
          timestamp: timestamp.toISOString(),
          method: 'clerk_user_profile'
        })
      });
  }

  //legal_accepted_at change detection
  if (eventData.legal_accepted_at !== undefined) {
    // Convert Unix timestamp to Date object
    const newLegalAcceptedAt = eventData.legal_accepted_at ? new Date(eventData.legal_accepted_at) : null;
    
    // Compare with existing date (convert to timestamp for comparison)
    const existingLegalAcceptedAtTimestamp = existingUser.legal_accepted_at ? existingUser.legal_accepted_at.getTime() : null;
    const newLegalAcceptedAtTimestamp = newLegalAcceptedAt ? newLegalAcceptedAt.getTime() : null;
    
    if (newLegalAcceptedAtTimestamp !== existingLegalAcceptedAtTimestamp) {
      changes.updateData.legal_accepted_at = newLegalAcceptedAt;
      changes.hasChanges = true;
      changes.changeTypes.push('legal_accepted_at');
      changes.auditLogs.push({
        action: 'legal.accepted',
        details: JSON.stringify({
          message: `Legal accepted`,
          timestamp: timestamp.toISOString(),
          method: 'clerk_user_profile'
        })
      });
    }
  }

  // MFA enabled at change detection
  if (eventData.mfa_enabled_at !== undefined) {
    // Convert Unix timestamp to Date object
    const newMfaEnabledAt = eventData.mfa_enabled_at ? new Date(eventData.mfa_enabled_at) : null;
    
    // Compare with existing date (convert to timestamp for comparison)
    const existingMfaEnabledAtTimestamp = existingUser.mfaEnabledAt ? existingUser.mfaEnabledAt.getTime() : null;
    const newMfaEnabledAtTimestamp = newMfaEnabledAt ? newMfaEnabledAt.getTime() : null;
    
    if (newMfaEnabledAtTimestamp !== existingMfaEnabledAtTimestamp) {
      changes.updateData.mfaEnabledAt = newMfaEnabledAt;
      changes.hasChanges = true;
      changes.changeTypes.push('mfaEnabledAt');
      changes.auditLogs.push({
        action: 'mfa.enabled',
        details: JSON.stringify({
          message: `MFA enabled`,
          timestamp: timestamp.toISOString(),
          method: 'clerk_user_profile'
        })
      });
    }
  }

  // MFA disabled at change detection
  if (eventData.mfa_disabled_at !== undefined) {
    // Convert Unix timestamp to Date object
    const newMfaDisabledAt = eventData.mfa_disabled_at ? new Date(eventData.mfa_disabled_at) : null;
    
    // Compare with existing date (convert to timestamp for comparison)
    const existingMfaDisabledAtTimestamp = existingUser.mfaDisabledAt ? existingUser.mfaDisabledAt.getTime() : null;
    const newMfaDisabledAtTimestamp = newMfaDisabledAt ? newMfaDisabledAt.getTime() : null;
    
    if (newMfaDisabledAtTimestamp !== existingMfaDisabledAtTimestamp) {
      changes.updateData.mfaDisabledAt = newMfaDisabledAt;
      changes.hasChanges = true;
      changes.changeTypes.push('mfaDisabledAt');
      changes.auditLogs.push({
        action: 'mfa.disabled',
        details: JSON.stringify({
          message: `MFA disabled`,
          timestamp: timestamp.toISOString(),
          method: 'clerk_user_profile'
        })
      });
    }
  }

  // Two-factor authentication change detection
  if (eventData.two_factor_enabled !== undefined && 
      eventData.two_factor_enabled !== existingUser.twoFactorEnabled) {
    changes.updateData.twoFactorEnabled = eventData.two_factor_enabled;
    changes.hasChanges = true;
    changes.changeTypes.push('2fa');
    changes.auditLogs.push({
      action: eventData.two_factor_enabled ? '2fa.enabled' : '2fa.disabled',
      details: JSON.stringify({
        message: eventData.two_factor_enabled ? 
          'Two-factor authentication enabled' : 
          'Two-factor authentication disabled',
        timestamp: timestamp.toISOString()
      })
    });
  }

  // Phone number verification detection
  if (eventData.phone_numbers && Array.isArray(eventData.phone_numbers)) {
    const phoneNumbers = eventData.phone_numbers as ClerkPhoneNumber[];
    const verifiedPhones = phoneNumbers.filter(
      (phone: ClerkPhoneNumber) => 
                      phone.verification?.status === 'verified' &&
                      phone.verification?.strategy === 'phone_code' && 
                      phone.verification?.channel === 'sms'
    );
    
    if (verifiedPhones.length > 0) {
      changes.changeTypes.push('phone');
      changes.auditLogs.push({
        action: 'phone.verified',
        details: JSON.stringify({
          message: 'Phone number verified',
          phoneCount: verifiedPhones.length,
          timestamp: timestamp.toISOString()
        })
      });
    }
  }

  return changes;
}

export async function createAuditLogsForChanges(
  auditLogs: AuditLogEntry[], 
  userId: string, 
  headerPayload: ReadonlyHeaders
): Promise<void> {
  if (auditLogs.length === 0) return;

  const auditData: AuditLogData[] = auditLogs.map((log: AuditLogEntry) => ({
    userId: userId,
    action: log.action,
    entityType: 'user',
    entityId: userId,
    details: log.details,
    ipAddress: headerPayload.get('x-forwarded-for') || null,
    userAgent: headerPayload.get('user-agent') || null,
    createdAt: new Date(),
    performedBy: 'clerkWebhook',
  }));

  await withRetry(() => prisma.userAuditLog.createMany({
    data: auditData
  }));
}

// Type guard to check if private metadata could contain notification preferences
function hasNotificationPreferences(metadata: unknown): metadata is PrivateMetaData {
  return metadata !== null && typeof metadata === 'object';
}

// Type guard to check if a value is a valid notification preference
function isValidNotificationPreference(value: unknown): value is boolean {
  return typeof value === 'boolean';
}

// Helper function to safely extract boolean from unknown type
function extractBooleanPreference(value: unknown, defaultValue: boolean = false): boolean {
  if (isValidNotificationPreference(value)) {
    return value;
  }
  return defaultValue;
}

function maskPhoneNumber(phoneNumber: string): string {
  if (!phoneNumber) return '';
  
  // Keep country code and last 4 digits visible
  const cleaned = phoneNumber.replace(/\D/g, '');
  if (cleaned.length <= 4) return phoneNumber;
  
  const lastFour = cleaned.slice(-4);
  const countryCodeLength = phoneNumber.startsWith('+') ? 2 : 0;
  const prefix = phoneNumber.slice(0, countryCodeLength + 2);
  
  return `${prefix}****${lastFour}`;
}