"use client";

import { useState, useTransition, useEffect, useRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Loader2, Phone, X, Check, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { useUser, useReverification } from "@clerk/nextjs";
import { formatPhoneNumber } from "@/lib/utils";
import { getClerkErrorMessage } from "@/lib/password";
import { isClerkRuntimeError, isReverificationCancelledError } from "@clerk/nextjs/errors";

// Phone number validation schema
const phoneNumberSchema = z.object({
  phoneNumber: z.string()
    .optional()
    .refine((val) => {
      if (!val || val.trim() === '') return true; // Allow empty
      // E.164 format: +[country code][subscriber number]
      const e164Regex = /^\+[1-9]\d{1,14}$/;
      return e164Regex.test(val);
    }, {
      message: "Numărul de telefon trebuie să fie în format internațional (ex: +40712345678)"
    }),
});

// Verification code schema
const verificationSchema = z.object({
  code: z.string().min(6, "Codul de verificare trebuie să aibă 6 caractere").max(6, "Codul de verificare trebuie să aibă 6 caractere"),
});

type PhoneNumberInput = z.infer<typeof phoneNumberSchema>;
type VerificationInput = z.infer<typeof verificationSchema>;

interface PhoneNumberFieldProps {
  initialPhoneNumber: string | null;
  verified: boolean;
}

export default function PhoneNumberField({ initialPhoneNumber, verified }: PhoneNumberFieldProps) {
  const { user } = useUser();
  const [isPending, startTransition] = useTransition();
  const [isVerificationDialogOpen, setIsVerificationDialogOpen] = useState(false);
  const [pendingPhoneNumber, setPendingPhoneNumber] = useState<string | null>(null);
  const [pendingPhoneId, setPendingPhoneId] = useState<string | null>(null);
  const [verificationAttempts, setVerificationAttempts] = useState(0);
  const [isVerifying, setIsVerifying] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    setValue,
    watch,
    reset
  } = useForm<PhoneNumberInput>({
    resolver: zodResolver(phoneNumberSchema),
    defaultValues: {
      phoneNumber: initialPhoneNumber || "",
    }
  });

  const {
    register: registerVerification,
    handleSubmit: handleVerificationSubmit,
    formState: { errors: verificationErrors },
    reset: resetVerification,
    setError: setVerificationError
  } = useForm<VerificationInput>({
    resolver: zodResolver(verificationSchema),
  });

  const currentPhoneNumber = watch("phoneNumber");

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  // Start countdown timer
  const startTimer = (seconds: number) => {
    setTimeLeft(seconds);
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    timerRef.current = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          if (timerRef.current) {
            clearInterval(timerRef.current);
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Handle phone number update with reverification
  const updatePhoneNumber = useReverification(async (phoneNumber: string) => {
    if (!user) throw new Error("User not authenticated");

    const formattedPhone = phoneNumber ? formatPhoneNumber(phoneNumber) : '';
    const existingPhoneNumbers = user.phoneNumbers || [];
    const primaryPhone = existingPhoneNumbers[0];

    // If removing phone number
    if (!formattedPhone && primaryPhone) {
      await primaryPhone.destroy();
      await user.reload();
      return { success: true, requiresVerification: false };
    }

    // If adding/updating phone number
    if (formattedPhone) {
      // Remove existing phone number if present
      if (primaryPhone) {
        await primaryPhone.destroy();
        await user.reload();
      }

      // Create new phone number
      const newPhoneObj = await user.createPhoneNumber({
        phoneNumber: formattedPhone
      });

      await user.reload();

      // Find the newly created phone number
      const createdPhone = user.phoneNumbers?.find(phone =>
        phone.id === newPhoneObj?.id
      );

      if (createdPhone) {
        // Prepare verification
        await createdPhone.prepareVerification();
        return {
          success: true,
          requiresVerification: true,
          phoneId: createdPhone.id,
          phoneNumber: formattedPhone
        };
      }
    }

    return { success: true, requiresVerification: false };
  });

  const onSubmit = (data: PhoneNumberInput) => {
    if (!user) return;

    const formattedPhone = data.phoneNumber ? formatPhoneNumber(data.phoneNumber) : '';
    const currentFormatted = initialPhoneNumber ? formatPhoneNumber(initialPhoneNumber) : '';

    // No change
    if (formattedPhone === currentFormatted) {
      return;
    }

    startTransition(async () => {
      try {
        const result = await updatePhoneNumber(formattedPhone);

        if (result.requiresVerification && result.phoneId) {
          // Open verification dialog
          setPendingPhoneNumber(result.phoneNumber || formattedPhone);
          setPendingPhoneId(result.phoneId);
          setIsVerificationDialogOpen(true);
          setVerificationAttempts(0);
          startTimer(60); // 60 seconds for first attempt
          toast.info("Cod de verificare trimis prin SMS");
        } else {
          // Phone number updated successfully without verification needed
          toast.success(formattedPhone ? "Numărul de telefon a fost actualizat" : "Numărul de telefon a fost eliminat");
          reset({ phoneNumber: formattedPhone });
        }
      } catch (e) {
        if (isClerkRuntimeError(e) && isReverificationCancelledError(e)) {
          console.error('User cancelled reverification', e.code);
          toast.error("Verificarea a fost anulată");
        } else {
          toast.error(getClerkErrorMessage(e));
        }
        // Reset form to original value
        reset({ phoneNumber: initialPhoneNumber || "" });
      }
    });
  };

  // Handle verification code submission
  const onVerificationSubmit = (data: VerificationInput) => {
    if (!user || !pendingPhoneId) return;

    setIsVerifying(true);

    const phoneNumber = user.phoneNumbers?.find(phone => phone.id === pendingPhoneId);
    if (!phoneNumber) {
      toast.error("Numărul de telefon nu a fost găsit");
      setIsVerifying(false);
      return;
    }

    phoneNumber.attemptVerification({ code: data.code })
      .then(async (result) => {
        if (result.verification?.status === 'verified') {
          await user.reload();
          toast.success("Numărul de telefon a fost verificat cu succes!");
          setIsVerificationDialogOpen(false);
          reset({ phoneNumber: pendingPhoneNumber || "" });
          resetVerification();
          setPendingPhoneNumber(null);
          setPendingPhoneId(null);
          if (timerRef.current) {
            clearInterval(timerRef.current);
          }
        } else {
          throw new Error("Verification failed");
        }
      })
      .catch((error) => {
        console.error("Verification error:", error);
        setVerificationAttempts(prev => prev + 1);

        if (verificationAttempts >= 2) {
          // After 3 failed attempts, delete the unverified phone number
          handleVerificationFailure();
        } else {
          setVerificationError("code", {
            message: "Cod de verificare incorect. Încercați din nou."
          });
        }
      })
      .finally(() => {
        setIsVerifying(false);
      });
  };

  // Handle verification failure - delete unverified phone number
  const handleVerificationFailure = async () => {
    if (!user || !pendingPhoneId) return;

    try {
      const phoneNumber = user.phoneNumbers?.find(phone => phone.id === pendingPhoneId);
      if (phoneNumber) {
        await phoneNumber.destroy();
        await user.reload();
      }

      toast.error("Verificarea a eșuat. Numărul de telefon a fost eliminat.");
      setIsVerificationDialogOpen(false);
      reset({ phoneNumber: initialPhoneNumber || "" });
      resetVerification();
      setPendingPhoneNumber(null);
      setPendingPhoneId(null);
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    } catch (error) {
      console.error("Error deleting unverified phone:", error);
      toast.error("Eroare la eliminarea numărului de telefon neverificat");
    }
  };

  // Handle dialog close - delete unverified phone number
  const handleDialogClose = (open: boolean) => {
    if (!open && pendingPhoneId) {
      // User closed dialog, delete unverified phone number
      handleVerificationFailure();
    }
  };

  // Resend verification code
  const resendVerificationCode = async () => {
    if (!user || !pendingPhoneId || timeLeft > 0) return;

    try {
      const phoneNumber = user.phoneNumbers?.find(phone => phone.id === pendingPhoneId);
      if (phoneNumber) {
        await phoneNumber.prepareVerification();
        toast.success("Cod de verificare retrimis");
        startTimer(60);
        resetVerification();
      }
    } catch (error) {
      console.error("Error resending code:", error);
      toast.error("Eroare la retrimiterea codului");
    }
  };

  // Format time for display
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <>
      <div className="space-y-2">
        <Label htmlFor="phoneNumber">
          Număr de telefon
          {isDirty && (
            <span className="text-sm text-gray-500 ml-2">
              (necesită verificare)
            </span>
          )}
        </Label>
        <div className="flex gap-2">
          <Input
            id="phoneNumber"
            type="tel"
            {...register("phoneNumber")}
            className={errors.phoneNumber ? "border-red-500" : ""}
            disabled={isPending}
            placeholder="+40712345678"
            onBlur={(e) => {
              // Auto-format on blur
              const formatted = formatPhoneNumber(e.target.value);
              if (formatted !== e.target.value) {
                setValue("phoneNumber", formatted);
              }
            }}
          />
          <Button
            type="button"
            onClick={handleSubmit(onSubmit)}
            disabled={isPending || !isDirty}
            className="bg-[#0066B1] hover:bg-[#004d85] text-white whitespace-nowrap"
          >
            {isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Se actualizează...
              </>
            ) : (
              <>
                <Phone className="mr-2 h-4 w-4" />
                {currentPhoneNumber ? "Actualizează" : "Adaugă"}
              </>
            )}
          </Button>
        </div>
        {errors.phoneNumber && (
          <p className="text-sm text-red-600">{errors.phoneNumber.message}</p>
        )}
      </div>

      {/* Verification Dialog */}
      <Dialog open={isVerificationDialogOpen} onOpenChange={handleDialogClose}>
        <DialogContent className="sm:max-w-md" showCloseButton={false}>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              Verificare număr de telefon
            </DialogTitle>
            <DialogDescription>
              Am trimis un cod de verificare prin SMS la numărul{" "}
              <span className="font-medium">{pendingPhoneNumber}</span>
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleVerificationSubmit(onVerificationSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="verificationCode">Cod de verificare</Label>
              <Input
                id="verificationCode"
                {...registerVerification("code")}
                className={verificationErrors.code ? "border-red-500" : ""}
                disabled={isVerifying}
                placeholder="123456"
                maxLength={6}
                autoComplete="one-time-code"
                autoFocus
              />
              {verificationErrors.code && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {verificationErrors.code.message}
                </p>
              )}
            </div>

            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>Încercări: {verificationAttempts}/3</span>
              {timeLeft > 0 ? (
                <span>Retrimitere în {formatTime(timeLeft)}</span>
              ) : (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={resendVerificationCode}
                  disabled={isVerifying}
                >
                  Retrimite codul
                </Button>
              )}
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => handleDialogClose(false)}
                disabled={isVerifying}
              >
                <X className="mr-2 h-4 w-4" />
                Anulează
              </Button>
              <Button
                type="submit"
                disabled={isVerifying}
                className="bg-[#0066B1] hover:bg-[#004d85] text-white"
              >
                {isVerifying ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Se verifică...
                  </>
                ) : (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Verifică
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
}

